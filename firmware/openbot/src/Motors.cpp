#include "../include/Motors.h"

Motors::Motors(Config* config)
    : config(config),
      currentPwmLeft(0),
      currentPwmRight(0),
      lastMotorUpdateTime(0) {
}

int Motors::getCurrentPwmLeft() const {
    return currentPwmLeft;
}

int Motors::getCurrentPwmRight() const {
    return currentPwmRight;
}

void Motors::updateVehicleWithAdjustments(PWMControlValues pwmValues) {
    // Get current time
    unsigned long now = millis();

    // Only update if enough time has passed since the last motor update
    if (now - lastMotorUpdateTime >= motorUpdateInterval) {
        // Update the timestamp
        lastMotorUpdateTime = now;

        // Save the computed PWM values
        currentPwmLeft = pwmValues.leftPwm;
        currentPwmRight = pwmValues.rightPwm;

        // Left motor
        digitalWrite(config->getPinDirectionL(), (currentPwmLeft >= 0) ? LOW : HIGH);
        analogWrite(config->getPinPwmL1(), abs(currentPwmLeft));

        // Right motor
        digitalWrite(config->getPinDirectionR(), (currentPwmRight >= 0) ? HIGH : LOW);
        analogWrite(config->getPinPwmR1(), abs(currentPwmRight));
    }
}
