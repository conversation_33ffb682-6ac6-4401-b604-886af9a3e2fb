#ifndef MOTORS_H
#define MOTORS_H

#include "Config.h"

class Motors {
public:
    Motors(Config* config);

    // Method to update vehicle using pre-calculated motor adjustments with timing control
    void updateVehicleWithAdjustments(PWMControlValues pwmValues);

    // Get current PWM values (for SatiBot V1)
    int getCurrentPwmLeft() const;
    int getCurrentPwmRight() const;

private:
    Config* config;

    // For SatiBot V1 with acceleration
    int currentPwmLeft;
    int currentPwmRight;

    // Motor update timing
    unsigned long lastMotorUpdateTime;
    static constexpr unsigned long motorUpdateInterval = 20;  // 20ms = 50Hz update rate
};

struct PWMControlValues {
    int leftPwm;
    int rightPwm;
};

#endif // MOTORS_H
